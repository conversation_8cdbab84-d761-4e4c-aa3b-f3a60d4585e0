import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Recon Extension - Coming Soon",
  description: "The Recon browser extension is coming soon. Stay tuned for updates.",
};

export default function ExtensionPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-back-neutral-primary to-back-neutral-secondary">
      <div className="mx-auto max-w-4xl px-6 text-center">
        <h1 className="mb-6 text-4xl font-bold text-fore-neutral-primary md:text-6xl">
          Recon Extension
        </h1>
        <p className="mb-8 text-xl text-fore-neutral-secondary md:text-2xl">
          Coming Soon
        </p>
        <p className="mb-12 text-lg text-fore-neutral-tertiary">
          We're working on an amazing browser extension that will revolutionize how you interact with smart contracts. 
          Stay tuned for updates!
        </p>
        <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
          <a
            href="/"
            className="rounded-lg bg-accent-primary px-6 py-3 text-fore-on-accent-primary transition-colors hover:bg-accent-primary/90"
          >
            Back to Home
          </a>
          <a
            href="/pro"
            className="rounded-lg border border-stroke-neutral-decorative px-6 py-3 text-fore-neutral-primary transition-colors hover:bg-back-neutral-tertiary"
          >
            Explore Recon PRO
          </a>
        </div>
      </div>
    </div>
  );
}
