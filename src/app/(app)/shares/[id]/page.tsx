"use client";

import axios from "axios";
import { use, useEffect, useState } from "react";

import SingleJobContainer from "@/app/components/SingleJobContainer";
import type { Job } from "@/app/services/jobs.hooks";

export default function SingleSharePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const [message, setMessage] = useState("");
  const [jobData, setJobData] = useState<Job | null>(null);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    async function getJobInfo() {
      if (!id) {
        return;
      }

      setLoading(true);
      try {
        const res = await axios.get(`/api/shares/${id}`);
        setJobData(res?.data?.data?.job);
      } catch (e) {
        if (e.response?.data?.message) {
          setMessage(e.response?.data?.message);
        } else {
          setMessage("Exception, check console");
          console.log(e);
        }
      }

      setLoading(false);
    }
    getJobInfo();
  }, [id]);

  const [toggleData, setToggleData] = useState(false);

  // TODO: Make share

  // TODO: Fetch if shared

  // Get single Share -> You can just fetch all for now and check if one of them matches
  // Long term: Separate fetching all (Separate page, super expensive)
  // With fetching one, fast and simple

  return (
    <div className="min-h-screen grow overflow-y-auto bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <SingleJobContainer
        isJobInfoLoading={isLoading}
        shareInfo={{ id }}
        jobId={jobData?.id}
        reloadShares={() => {}}
        jobData={jobData}
        isLoading={isLoading}
      />
    </div>
  );
}
