[⠊] Compiling...
[⠆] Compiling 45 files with Solc 0.8.30
[⠔] Solc 0.8.30 finished in 2.32s
Compiler run successful!
note[unaliased-plain-import]: use named imports '{A, B}' or alias 'import ".." as X'
 --> /Users/<USER>/Code/create-chimera-app/test/recon/CryticToFoundry.sol:6:8
  |
6 | import "forge-std/console2.sol";
  |        ------------------------
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unaliased-plain-import

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/Counter.t.sol:4:15
  |
4 | import {Test, console} from "forge-std/Test.sol";
  |               -------
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[mixed-case-variable]: mutable variables should use mixedCase
 --> /Users/<USER>/Code/create-chimera-app/test/recon/BeforeAfter.sol:9:17
  |
9 |         uint256 counter_number;
  |                 --------------
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-variable

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/AdminTargets.sol:18:14
   |
18 |     function counter_increment_asAdmin() public updateGhosts asAdmin {
   |              -------------------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/AdminTargets.sol:5:9
  |
5 | import {BeforeAfter} from "../BeforeAfter.sol";
  |         -----------
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/AdminTargets.sol:7:9
  |
7 | import {vm} from "@chimera/Hevm.sol";
  |         --
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/TargetFunctions.sol:21:14
   |
21 |     function counter_increment() public updateGhosts asActor {
   |              -----------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/TargetFunctions.sol:25:14
   |
25 |     function counter_setNumber1(uint256 newNumber) public updateGhosts asActor {
   |              ------------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/script/Counter.s.sol:4:17
  |
4 | import {Script, console} from "forge-std/Script.sol";
  |                 -------
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/ManagersTargets.sol:28:14
   |
28 |     function switch_asset(uint256 entropy) public {
   |              ------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/ManagersTargets.sol:33:14
   |
33 |     function add_new_asset(uint8 decimals) public returns (address) {
   |              -------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/ManagersTargets.sol:44:14
   |
44 |     function asset_approve(address to, uint128 amt) public updateGhosts asActor {
   |              -------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/ManagersTargets.sol:49:14
   |
49 |     function asset_mint(address to, uint128 amt) public updateGhosts asAdmin {
   |              ----------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[unaliased-plain-import]: use named imports '{A, B}' or alias 'import ".." as X'
  --> /Users/<USER>/Code/create-chimera-app/test/recon/Setup.sol:16:8
   |
16 | import "src/Counter.sol";
   |        -----------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#unaliased-plain-import

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/TargetFunctions.sol:42:14
   |
42 |     function counter_setNumber2(uint256 newNumber) public asActor {
   |              ------------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/recon/TargetFunctions.sol:5:9
  |
5 | import {vm} from "@chimera/Hevm.sol";
  |         --
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[mixed-case-function]: function names should use mixedCase
  --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/DoomsdayTargets.sol:22:14
   |
22 |     function doomsday_increment_never_reverts() public stateless asAdmin {
   |              --------------------------------
   |
   = help: https://book.getfoundry.sh/reference/forge/forge-lint#mixed-case-function

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/DoomsdayTargets.sol:5:9
  |
5 | import {BeforeAfter} from "../BeforeAfter.sol";
  |         -----------
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/DoomsdayTargets.sol:7:9
  |
7 | import {vm} from "@chimera/Hevm.sol";
  |         --
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/ManagersTargets.sol:5:9
  |
5 | import {BeforeAfter} from "../BeforeAfter.sol";
  |         -----------
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import

note[unused-import]: unused imports should be removed
 --> /Users/<USER>/Code/create-chimera-app/test/recon/targets/ManagersTargets.sol:7:9
  |
7 | import {vm} from "@chimera/Hevm.sol";
  |         --
  |
  = help: https://book.getfoundry.sh/reference/forge/forge-lint#unused-import


Running 1 tests for test/recon/CryticToFoundry.sol:CryticToFoundry


╭─────────────────────── Initial Invariant Target Functions ───────────────────────╮
│ CryticToFoundry.sol:CryticToFoundry @ 0x7fa9385be102ac3eac297483dd6233d62b3e1496 │
│ ├── add_new_asset(uint8)                                                         │
│ ├── asset_approve(address,uint128)                                               │
│ ├── asset_mint(address,uint128)                                                  │
│ ├── counter_increment()                                                          │
│ ├── counter_increment_asAdmin()                                                  │
│ ├── counter_setNumber1(uint256)                                                  │
│ ├── counter_setNumber2(uint256)                                                  │
│ ├── doomsday_increment_never_reverts()                                           │
│ ├── switchActor(uint256)                                                         │
│ └── switch_asset(uint256)                                                        │
╰──────────────────────────────────────────────────────────────────────────────────╯


Assertion failure detected in CryticToFoundry.doomsday_increment_never_reverts()
Counterexample: 
    halmos_block_timestamp_depth1_e7954f8 = 0x8000000000000000
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_7b86598_23 = 0x00
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_b5e670a_55 = 0x00
    p_newNumber_uint256_f514ead_24 = 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
Sequence:
    CALL CryticToFoundry::counter_setNumber1(p_newNumber_uint256_f514ead_24) (value: halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_7b86598_23) 
(caller: halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_0b44ffb_22)
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → 0x0000000000000000000000000000000000000000000000000000000000000001
        ↩ RETURN 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @7 → 0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @7 ← 0x0000000000000000000000000000000000000000000000000000000000000001
        SLOAD  @0 → 0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496
        CALL hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) (caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        CALL 0xaaaa0003::setNumber(p_newNumber_uint256_f514ead_24) (caller: CryticToFoundry)
            SLOAD  @0 → 0x0000000000000000000000000000000000000000000000000000000000000001
            SSTORE @0 ← p_newNumber_uint256_f514ead_24
        ↩ RETURN 0x
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → p_newNumber_uint256_f514ead_24
        ↩ RETURN p_newNumber_uint256_f514ead_24
        STATICCALL 
hevm::assertTrue(0x0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000004000000000000
000000000000000000000000000000000000000000000000000136e756d62657220213d206e65774e756d62657200000000000000000000000000) [static] (caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        STATICCALL 0xaaaa0003::number() [static] (caller: CryticToFoundry)
            SLOAD  @0 → p_newNumber_uint256_f514ead_24
        ↩ RETURN p_newNumber_uint256_f514ead_24
        SLOAD  @8 → 0x0000000000000000000000000000000000000000000000000000000000000000
        SSTORE @8 ← p_newNumber_uint256_f514ead_24
    ↩ RETURN 0x
    CALL CryticToFoundry::doomsday_increment_never_reverts() (value: halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_b5e670a_55) (caller: 
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_6249d6a_54)
        CALL hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) (caller: CryticToFoundry)
        ↩ 0x
        SLOAD  @6 → 0x00000000000000000000000000000000000000000000000000000000aaaa0003
        CALL 0xaaaa0003::increment() (caller: CryticToFoundry)
            SLOAD  @0 → p_newNumber_uint256_f514ead_24
        ↩ REVERT 0x4e487b710000000000000000000000000000000000000000000000000000000000000011 (error: Revert())
    ↩ STATICCALL 0x (error: FailCheatcode("VmAssertion(cond=False, msg='doomsday_increment_never_reverts')"))

[PASS] invariant_number_never_zero() (paths: 100, time: 2.28s, bounds: [])
Symbolic test result: 1 passed; 0 failed; time: 2.38s
